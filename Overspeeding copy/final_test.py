#!/usr/bin/env python3
"""
FINAL TEST - Fixed Dashcam Overspeeding Detection
This test should work without any tensor dimension errors
"""

import cv2
import numpy as np
import os
import sys
import time
from dashcam_speed_detector import DashcamSpeedDetector

def test_fixed_system():
    """Test the fixed speed detection system"""
    print("🚀 FINAL TEST - Dashcam Overspeeding Detection")
    print("=" * 60)
    
    video_path = "speed.mp4"
    
    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        return False
    
    try:
        print("🔧 Initializing detector (OpenCV-only mode)...")
        detector = DashcamSpeedDetector(
            pwc_model_path="pwc_net.pth",  # Will be ignored
            yolo_model_path="yolov8n.pt",
            speed_model_type="nvidia",
            device="cpu"
        )
        print("✅ Detector initialized successfully!")
        
        # Test with a few frames first
        print("\n📹 Testing with video frames...")
        cap = cv2.VideoCapture(video_path)
        
        frame_count = 0
        speeds = []
        
        while frame_count < 10:  # Test with 10 frames
            ret, frame = cap.read()
            if not ret:
                break
            
            try:
                processed_frame, ego_speed, vehicles = detector.process_frame(frame)
                speeds.append(ego_speed)
                
                print(f"   Frame {frame_count + 1}: Speed = {ego_speed:.1f} km/h, Vehicles = {len(vehicles)}")
                frame_count += 1
                
            except Exception as e:
                print(f"   ❌ Error processing frame {frame_count + 1}: {e}")
                break
        
        cap.release()
        
        if frame_count > 0:
            avg_speed = sum(speeds) / len(speeds)
            max_speed = max(speeds)
            print(f"\n📊 Frame test results:")
            print(f"   Frames processed: {frame_count}")
            print(f"   Average speed: {avg_speed:.1f} km/h")
            print(f"   Max speed: {max_speed:.1f} km/h")
            print("✅ Frame processing test PASSED!")
        else:
            print("❌ No frames processed successfully")
            return False
        
        # Now test full video processing
        print(f"\n🎬 Processing full video (first 50 frames)...")
        output_path = "final_test_output.mp4"
        
        start_time = time.time()
        results = detector.process_video(
            video_path=video_path,
            output_path=output_path,
            max_frames=50
        )
        processing_time = time.time() - start_time
        
        print(f"\n📊 FINAL RESULTS:")
        print(f"   ✅ Processing completed successfully!")
        print(f"   📹 Frames processed: {results['total_frames']}")
        print(f"   ⏱️  Processing time: {processing_time:.2f} seconds")
        print(f"   🏃 Average FPS: {results['total_frames']/processing_time:.1f}")
        print(f"   🚗 Average ego speed: {results['avg_ego_speed']:.1f} km/h")
        print(f"   🏎️  Maximum ego speed: {results['max_ego_speed']:.1f} km/h")
        print(f"   🚨 Violations detected: {len(results['violation_frames'])}")
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)
            print(f"   📁 Output video: {output_path} ({file_size:.1f} MB)")
        
        print(f"\n🎉 ALL TESTS PASSED! System is working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_ready():
    """Test if API is ready to run"""
    print(f"\n🌐 API READINESS TEST")
    print("=" * 30)
    
    try:
        from dashcam_api import app
        print("✅ API module imports successfully")
        
        print(f"\n📋 To start the API server:")
        print(f"   python3 dashcam_api.py")
        print(f"   Server will run on: http://localhost:8007")
        
        print(f"\n🧪 To test the API:")
        print(f"   curl -X POST 'http://localhost:8007/process-video/' \\")
        print(f"        -H 'Content-Type: multipart/form-data' \\")
        print(f"        -F 'file=@speed.mp4'")
        
        return True
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def show_final_status():
    """Show final system status"""
    print(f"\n📋 SYSTEM STATUS")
    print("=" * 30)
    
    # Check required files
    required_files = [
        ("speed.mp4", "Test video"),
        ("yolov8n.pt", "YOLO model"),
        ("dashcam_speed_detector.py", "Main detector"),
        ("dashcam_api.py", "API server"),
        ("optical_flow_utils.py", "Optical flow utilities"),
        ("nvidia_cnn.py", "Speed estimation model")
    ]
    
    all_files_present = True
    for filename, description in required_files:
        if os.path.exists(filename):
            print(f"   ✅ {filename} - {description}")
        else:
            print(f"   ❌ {filename} - {description} (MISSING)")
            all_files_present = False
    
    print(f"\n🔧 SYSTEM CAPABILITIES:")
    print(f"   ✅ Vehicle detection (YOLOv8)")
    print(f"   ✅ Optical flow (OpenCV Farneback)")
    print(f"   ✅ Speed estimation (Simple flow-based)")
    print(f"   ✅ Video processing")
    print(f"   ✅ API server")
    print(f"   ⚠️  PWC-Net (Disabled due to compatibility)")
    
    return all_files_present

def main():
    """Main test function"""
    print("DASHCAM OVERSPEEDING DETECTION - FINAL SYSTEM TEST")
    print("=" * 70)
    
    # Show system status
    files_ok = show_final_status()
    
    if not files_ok:
        print(f"\n❌ Some required files are missing. Please check the file list above.")
        return 1
    
    # Run main test
    success = test_fixed_system()
    
    # Test API readiness
    api_ready = test_api_ready()
    
    # Final summary
    print(f"\n" + "=" * 70)
    print(f"FINAL SUMMARY")
    print("=" * 70)
    
    if success and api_ready:
        print(f"🎉 SUCCESS! The dashcam overspeeding detection system is fully functional!")
        print(f"\n✅ What's working:")
        print(f"   • Video processing with speed detection")
        print(f"   • Vehicle detection and tracking")
        print(f"   • Optical flow calculation (OpenCV)")
        print(f"   • Speed estimation and violation detection")
        print(f"   • API server ready to deploy")
        
        print(f"\n🚀 Next steps:")
        print(f"   1. Start API server: python3 dashcam_api.py")
        print(f"   2. Integrate with your FYP frontend")
        print(f"   3. Test with real dashcam videos")
        print(f"   4. Fine-tune speed calibration if needed")
        
        return 0
    else:
        print(f"❌ Some components failed. Check the error messages above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
