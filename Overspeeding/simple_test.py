#!/usr/bin/env python3
"""
Simple test script for dashcam speed detection
Tests the basic functionality without complex video processing
"""

import cv2
import numpy as np
import os
import sys
from dashcam_speed_detector import DashcamSpeedDetector

def create_simple_test_video(output_path="simple_test.mp4", duration=5, fps=30):
    """Create a simple test video with moving patterns"""
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    width, height = 640, 480
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    total_frames = duration * fps
    print(f"Creating simple test video: {output_path}")
    
    for frame_idx in range(total_frames):
        # Create frame with moving pattern
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add moving horizontal lines to simulate forward motion
        line_spacing = 20
        offset = (frame_idx * 2) % line_spacing
        
        for y in range(offset, height, line_spacing):
            cv2.line(frame, (0, y), (width, y), (100, 100, 100), 2)
        
        # Add some moving objects (cars)
        car_x = (frame_idx * 3) % (width + 100) - 50
        if 0 <= car_x <= width:
            cv2.rectangle(frame, (car_x, height//2), (car_x + 60, height//2 + 30), (0, 255, 0), -1)
        
        # Add frame number
        cv2.putText(frame, f"Frame: {frame_idx}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        out.write(frame)
    
    out.release()
    print(f"Test video created: {output_path}")
    return output_path

def test_basic_detection():
    """Test basic speed detection functionality"""
    print("=== Testing Basic Speed Detection ===")
    
    try:
        # Initialize detector
        detector = DashcamSpeedDetector(
            pwc_model_path="pwc_net.pth",
            yolo_model_path="yolov8n.pt",
            speed_model_type="nvidia",
            device="cpu"  # Use CPU for testing
        )
        print("✓ Detector initialized successfully")
        
        # Create test frames
        frame1 = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        frame2 = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Test single frame processing
        processed_frame, ego_speed, vehicles = detector.process_frame(frame1)
        print(f"✓ First frame processed, ego speed: {ego_speed:.1f} km/h")
        
        processed_frame, ego_speed, vehicles = detector.process_frame(frame2)
        print(f"✓ Second frame processed, ego speed: {ego_speed:.1f} km/h")
        
        print("✓ Basic detection test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Basic detection test failed: {e}")
        return False

def test_video_processing():
    """Test video processing functionality"""
    print("\n=== Testing Video Processing ===")
    
    try:
        # Create test video
        test_video_path = create_simple_test_video("simple_test.mp4", duration=3, fps=10)
        
        if not os.path.exists(test_video_path):
            print("✗ Failed to create test video")
            return False
        
        # Initialize detector
        detector = DashcamSpeedDetector(
            pwc_model_path="pwc_net.pth",
            yolo_model_path="yolov8n.pt",
            speed_model_type="nvidia",
            device="cpu"
        )
        
        # Process video
        output_path = "output_simple_test.mp4"
        results = detector.process_video(
            video_path=test_video_path,
            output_path=output_path,
            max_frames=20  # Limit for testing
        )
        
        print(f"✓ Video processed successfully")
        print(f"  - Frames processed: {results['total_frames']}")
        print(f"  - Average speed: {results['avg_ego_speed']:.1f} km/h")
        print(f"  - Max speed: {results['max_ego_speed']:.1f} km/h")
        print(f"  - Processing time: {results['total_time']:.2f}s")
        
        # Cleanup
        if os.path.exists(test_video_path):
            os.remove(test_video_path)
        
        print("✓ Video processing test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Video processing test failed: {e}")
        return False

def test_api_initialization():
    """Test API initialization"""
    print("\n=== Testing API Initialization ===")
    
    try:
        # Import and test API components
        from dashcam_api import app
        print("✓ API module imported successfully")
        
        # Test basic API functionality
        print("✓ API initialization test passed!")
        return True
        
    except Exception as e:
        print(f"✗ API initialization test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Starting Dashcam Speed Detection Tests")
    print("=" * 50)
    
    tests = [
        ("Basic Detection", test_basic_detection),
        ("Video Processing", test_video_processing),
        ("API Initialization", test_api_initialization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
