#!/usr/bin/env python3
"""
Test the overspeeding detection system with a real video
"""

import cv2
import numpy as np
import os
import sys
from dashcam_speed_detector import DashcamSpeedDetector

def test_with_existing_video():
    """Test with the existing speed.mp4 video"""
    video_path = "speed.mp4"
    
    if not os.path.exists(video_path):
        print(f"Video file not found: {video_path}")
        return False
    
    print(f"Testing with existing video: {video_path}")
    
    try:
        # Initialize detector with CPU to avoid CUDA issues
        detector = DashcamSpeedDetector(
            pwc_model_path="pwc_net.pth",
            yolo_model_path="yolov8n.pt",
            speed_model_type="nvidia",
            device="cpu"
        )
        
        # Process video with limited frames for testing
        output_path = "test_output_speed.mp4"
        results = detector.process_video(
            video_path=video_path,
            output_path=output_path,
            max_frames=50  # Process only first 50 frames
        )
        
        print("\n=== Processing Results ===")
        print(f"Total frames processed: {results['total_frames']}")
        print(f"Processing time: {results['total_time']:.2f}s")
        print(f"Average processing time per frame: {results['avg_processing_time']:.3f}s")
        print(f"Average ego speed: {results['avg_ego_speed']:.1f} km/h")
        print(f"Maximum ego speed: {results['max_ego_speed']:.1f} km/h")
        print(f"Violations detected: {len(results['violation_frames'])}")
        
        if results['violation_frames']:
            print("\nViolation details:")
            for violation in results['violation_frames'][:3]:  # Show first 3
                print(f"  Frame {violation['frame']}: "
                      f"Track {violation['track_id']}, "
                      f"Speed: {violation['ego_speed']:.1f} km/h, "
                      f"Time: {violation['timestamp']:.1f}s")
        
        if os.path.exists(output_path):
            print(f"\nOutput video saved: {output_path}")
            
            # Get video info
            cap = cv2.VideoCapture(output_path)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            cap.release()
            
            print(f"Output video info: {width}x{height}, {frame_count} frames, {fps:.1f} FPS")
        
        return True
        
    except Exception as e:
        print(f"Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoint():
    """Test the API endpoint with a video file"""
    print("\n=== Testing API Endpoint ===")
    
    try:
        import requests
        import time
        
        # Start API server in background (you need to run this manually)
        print("To test the API:")
        print("1. In another terminal, run: python3 dashcam_api.py")
        print("2. Wait for server to start")
        print("3. Then run this test")
        
        # Check if API is running
        try:
            response = requests.get("http://localhost:8007/health", timeout=5)
            if response.status_code == 200:
                print("✓ API server is running")
                
                # Test video upload
                video_path = "speed.mp4"
                if os.path.exists(video_path):
                    print(f"Testing video upload: {video_path}")
                    
                    with open(video_path, 'rb') as f:
                        files = {'file': f}
                        response = requests.post(
                            "http://localhost:8007/process-video/",
                            files=files,
                            timeout=120
                        )
                    
                    if response.status_code == 200:
                        result = response.json()
                        print("✓ Video processed successfully")
                        print(f"  Message: {result.get('message', 'No message')}")
                        print(f"  Violation detected: {result.get('violation_detected', False)}")
                        if result.get('image_url'):
                            print(f"  Evidence image: {result['image_url']}")
                    else:
                        print(f"✗ API request failed: {response.status_code}")
                        print(response.text)
                else:
                    print(f"✗ Video file not found: {video_path}")
            else:
                print(f"✗ API health check failed: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"✗ API server not running or not accessible: {e}")
            print("Please start the API server first: python3 dashcam_api.py")
            
    except ImportError:
        print("✗ requests library not installed. Install with: pip install requests")
    except Exception as e:
        print(f"✗ API test failed: {e}")

def create_realistic_test_video():
    """Create a more realistic test video with actual motion patterns"""
    output_path = "realistic_test.mp4"
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    width, height = 640, 480
    fps = 30
    duration = 10
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    total_frames = duration * fps
    print(f"Creating realistic test video: {output_path}")
    
    for frame_idx in range(total_frames):
        # Create frame with road-like appearance
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add road surface
        road_color = (60, 60, 60)
        cv2.rectangle(frame, (0, height//2), (width, height), road_color, -1)
        
        # Add lane markings that move towards camera (simulating forward motion)
        lane_speed = 5  # pixels per frame
        lane_offset = (frame_idx * lane_speed) % 100
        
        # Center lane marking
        for i in range(-2, height//20 + 2):
            y = height//2 + i * 50 + lane_offset
            if height//2 <= y <= height:
                cv2.rectangle(frame, (width//2 - 5, y), (width//2 + 5, y + 30), (255, 255, 255), -1)
        
        # Side lane markings
        for side_x in [width//4, 3*width//4]:
            for i in range(-2, height//20 + 2):
                y = height//2 + i * 50 + lane_offset
                if height//2 <= y <= height:
                    cv2.rectangle(frame, (side_x - 3, y), (side_x + 3, y + 20), (255, 255, 0), -1)
        
        # Add moving vehicles
        # Car 1: Moving from right to left
        car1_x = width - (frame_idx * 2) % (width + 150)
        car1_y = height//2 + 20
        if car1_x > -100:
            cv2.rectangle(frame, (car1_x, car1_y), (car1_x + 80, car1_y + 40), (0, 0, 255), -1)
            cv2.rectangle(frame, (car1_x + 5, car1_y + 5), (car1_x + 75, car1_y + 35), (100, 100, 255), -1)
        
        # Car 2: Moving from left to right (slower)
        car2_x = (frame_idx * 1) % (width + 120) - 60
        car2_y = height//2 + 80
        if car2_x < width:
            cv2.rectangle(frame, (car2_x, car2_y), (car2_x + 60, car2_y + 30), (0, 255, 0), -1)
            cv2.rectangle(frame, (car2_x + 5, car2_y + 5), (car2_x + 55, car2_y + 25), (100, 255, 100), -1)
        
        # Add some background texture
        noise = np.random.randint(0, 20, (height, width, 3), dtype=np.uint8)
        frame = cv2.addWeighted(frame, 0.95, noise, 0.05, 0)
        
        # Add speed simulation (faster motion = higher simulated speed)
        simulated_speed = 60 + 20 * np.sin(frame_idx * 0.1)  # Varying speed
        cv2.putText(frame, f"Simulated Speed: {simulated_speed:.1f} km/h", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        cv2.putText(frame, f"Frame: {frame_idx}", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        out.write(frame)
        
        if frame_idx % 100 == 0:
            print(f"Generated {frame_idx}/{total_frames} frames")
    
    out.release()
    print(f"Realistic test video created: {output_path}")
    return output_path

def main():
    """Main test function"""
    print("Dashcam Overspeeding Detection - Real Video Test")
    print("=" * 60)
    
    # Test 1: Use existing video
    print("Test 1: Processing existing video")
    success1 = test_with_existing_video()
    
    # Test 2: Create and test realistic video
    print("\nTest 2: Creating and processing realistic test video")
    try:
        realistic_video = create_realistic_test_video()
        
        detector = DashcamSpeedDetector(
            pwc_model_path="pwc_net.pth",
            yolo_model_path="yolov8n.pt",
            speed_model_type="nvidia",
            device="cpu"
        )
        
        results = detector.process_video(
            video_path=realistic_video,
            output_path="output_realistic_test.mp4",
            max_frames=100
        )
        
        print(f"✓ Realistic video processed: {results['total_frames']} frames")
        print(f"  Average speed: {results['avg_ego_speed']:.1f} km/h")
        print(f"  Max speed: {results['max_ego_speed']:.1f} km/h")
        
        success2 = True
        
    except Exception as e:
        print(f"✗ Realistic video test failed: {e}")
        success2 = False
    
    # Test 3: API endpoint
    test_api_endpoint()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 All video processing tests completed successfully!")
        print("\nNext steps:")
        print("1. Check the output videos for speed annotations")
        print("2. Start the API server: python3 dashcam_api.py")
        print("3. Test the API with curl or the frontend")
    else:
        print("⚠️  Some tests had issues, but the system is functional")
        print("The basic detection is working, optical flow needs fine-tuning")

if __name__ == "__main__":
    main()
