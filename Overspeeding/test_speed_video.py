#!/usr/bin/env python3
"""
Final test script for speed.mp4 video - Dashcam Overspeeding Detection
This is the definitive test for the overspeeding detection system
"""

import cv2
import numpy as np
import os
import sys
import time
from dashcam_speed_detector import DashcamSpeedDetector

def test_speed_video():
    """Test the speed detection system with speed.mp4"""
    print("=" * 60)
    print("DASHCAM OVERSPEEDING DETECTION - FINAL TEST")
    print("=" * 60)
    
    video_path = "speed.mp4"
    
    # Check if video exists
    if not os.path.exists(video_path):
        print(f"❌ ERROR: Video file not found: {video_path}")
        print("Please ensure speed.mp4 is in the current directory")
        return False
    
    # Get video info
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ ERROR: Cannot open video: {video_path}")
        return False
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    duration = total_frames / fps
    cap.release()
    
    print(f"📹 Video Info:")
    print(f"   File: {video_path}")
    print(f"   Resolution: {width}x{height}")
    print(f"   FPS: {fps:.1f}")
    print(f"   Total frames: {total_frames}")
    print(f"   Duration: {duration:.1f} seconds")
    print()
    
    try:
        print("🚀 Initializing Speed Detector...")
        detector = DashcamSpeedDetector(
            pwc_model_path="pwc_net.pth",
            yolo_model_path="yolov8n.pt",
            speed_model_type="nvidia",
            device="cpu"  # Use CPU for stability
        )
        print("✅ Detector initialized successfully!")
        print()
        
        # Process video
        output_path = "speed_output_final.mp4"
        print(f"🎬 Processing video...")
        print(f"   Input: {video_path}")
        print(f"   Output: {output_path}")
        print(f"   Processing frames: 100 (for testing)")
        print()
        
        start_time = time.time()
        
        results = detector.process_video(
            video_path=video_path,
            output_path=output_path,
            max_frames=100  # Process first 100 frames for testing
        )
        
        processing_time = time.time() - start_time
        
        print("✅ Video processing completed!")
        print()
        print("📊 RESULTS:")
        print(f"   Frames processed: {results['total_frames']}")
        print(f"   Processing time: {processing_time:.2f} seconds")
        print(f"   Average FPS: {results['total_frames']/processing_time:.1f}")
        print(f"   Average ego speed: {results['avg_ego_speed']:.1f} km/h")
        print(f"   Maximum ego speed: {results['max_ego_speed']:.1f} km/h")
        print(f"   Violations detected: {len(results['violation_frames'])}")
        print()
        
        # Show violation details
        if results['violation_frames']:
            print("🚨 VIOLATION DETAILS:")
            for i, violation in enumerate(results['violation_frames'][:5]):
                print(f"   {i+1}. Frame {violation['frame']}: "
                      f"Track {violation['track_id']}, "
                      f"Speed: {violation['ego_speed']:.1f} km/h, "
                      f"Time: {violation['timestamp']:.1f}s")
            if len(results['violation_frames']) > 5:
                print(f"   ... and {len(results['violation_frames']) - 5} more violations")
        else:
            print("✅ No overspeeding violations detected")
        print()
        
        # Check output video
        if os.path.exists(output_path):
            output_cap = cv2.VideoCapture(output_path)
            if output_cap.isOpened():
                output_frames = int(output_cap.get(cv2.CAP_PROP_FRAME_COUNT))
                output_cap.release()
                print(f"📹 Output video created successfully:")
                print(f"   File: {output_path}")
                print(f"   Frames: {output_frames}")
                print(f"   Size: {os.path.getsize(output_path) / (1024*1024):.1f} MB")
            else:
                print("⚠️  Output video created but cannot be opened")
        else:
            print("⚠️  Output video not created")
        
        print()
        print("🎉 TEST COMPLETED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"❌ ERROR during processing: {e}")
        import traceback
        print("\nFull error traceback:")
        traceback.print_exc()
        return False

def test_api_server():
    """Test the API server functionality"""
    print("\n" + "=" * 60)
    print("API SERVER TEST")
    print("=" * 60)
    
    try:
        print("🔧 Testing API module import...")
        from dashcam_api import app
        print("✅ API module imported successfully!")
        
        print("\n📋 To start the API server:")
        print("   1. Open a new terminal")
        print("   2. Navigate to the Overspeeding directory")
        print("   3. Run: python3 dashcam_api.py")
        print("   4. Server will be available at: http://localhost:8007")
        
        print("\n🧪 To test the API:")
        print("   curl -X POST 'http://localhost:8007/process-video/' \\")
        print("        -H 'accept: application/json' \\")
        print("        -H 'Content-Type: multipart/form-data' \\")
        print("        -F 'file=@speed.mp4'")
        
        return True
        
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def show_system_info():
    """Show system information"""
    print("\n" + "=" * 60)
    print("SYSTEM INFORMATION")
    print("=" * 60)
    
    print("📦 Required files:")
    files_to_check = [
        "speed.mp4",
        "pwc_net.pth", 
        "yolov8n.pt",
        "dashcam_speed_detector.py",
        "dashcam_api.py",
        "optical_flow_utils.py",
        "nvidia_cnn.py",
        "pwc_net.py"
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            size = os.path.getsize(file) / (1024*1024)
            print(f"   ✅ {file} ({size:.1f} MB)")
        else:
            print(f"   ❌ {file} (missing)")
    
    print("\n🔧 System capabilities:")
    try:
        import torch
        print(f"   ✅ PyTorch: {torch.__version__}")
        print(f"   ✅ CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   ✅ CUDA device: {torch.cuda.get_device_name(0)}")
    except:
        print("   ❌ PyTorch not available")
    
    try:
        import cv2
        print(f"   ✅ OpenCV: {cv2.__version__}")
    except:
        print("   ❌ OpenCV not available")
    
    try:
        from ultralytics import YOLO
        print("   ✅ Ultralytics YOLO available")
    except:
        print("   ❌ Ultralytics YOLO not available")

def main():
    """Main test function"""
    print("Starting Dashcam Overspeeding Detection Test")
    
    # Show system info
    show_system_info()
    
    # Test video processing
    success = test_speed_video()
    
    # Test API
    test_api_server()
    
    print("\n" + "=" * 60)
    print("FINAL SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("\nThe dashcam overspeeding detection system is working correctly.")
        print("\nNext steps:")
        print("1. ✅ Video processing works")
        print("2. 🚀 Start API server: python3 dashcam_api.py")
        print("3. 🌐 Integrate with frontend")
        print("4. 📱 Test with real dashcam videos")
    else:
        print("⚠️  SOME TESTS FAILED")
        print("\nThe system has issues that need to be resolved.")
        print("Check the error messages above for details.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
