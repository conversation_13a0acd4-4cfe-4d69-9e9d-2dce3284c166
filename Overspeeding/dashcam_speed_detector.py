"""
Dashcam Speed Detection System
Uses PWC-Net for optical flow and NVIDIA CNN for speed regression
Integrates YOLOv8 for vehicle detection and tracking
"""

import cv2
import numpy as np
import torch
import torch.nn.functional as F
from ultralytics import YOLO
import os
from typing import Tuple, List, Optional, Dict
from collections import defaultdict
import time

from pwc_net import load_pwc_net
from nvidia_cnn import create_speed_model
from optical_flow_utils import (
    frames_to_tensor, 
    calculate_speed_from_flow,
    estimate_ego_motion,
    scale_flow_for_cnn,
    visualize_flow_on_frame
)


class DashcamSpeedDetector:
    """
    Main class for dashcam speed detection using optical flow and deep learning
    """
    
    def __init__(self, 
                 pwc_model_path: str = "pwc_net.pth",
                 yolo_model_path: str = "yolov8n.pt",
                 speed_model_type: str = "nvidia",
                 device: str = "auto"):
        """
        Initialize the speed detector
        
        Args:
            pwc_model_path: Path to PWC-Net model
            yolo_model_path: Path to YOLO model
            speed_model_type: Type of speed model ('nvidia' or 'improved')
            device: Device to use ('cuda', 'cpu', or 'auto')
        """
        # Set device
        if device == "auto":
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)
        
        print(f"Using device: {self.device}")
        
        # Load models
        self.pwc_net = self._load_pwc_net(pwc_model_path)
        self.yolo_model = self._load_yolo_model(yolo_model_path)
        self.speed_model = self._load_speed_model(speed_model_type)
        
        # Speed estimation parameters
        self.speed_scale = 0.02  # Calibration factor
        self.speed_limit = 120   # Speed limit in km/h
        self.confidence_threshold = 0.5
        
        # Vehicle tracking
        self.vehicle_tracks = defaultdict(lambda: {
            'speeds': [],
            'positions': [],
            'last_seen': 0,
            'overspeeding': False
        })
        self.track_id_counter = 0
        
        # Video processing
        self.frame_count = 0
        self.prev_frame = None
        
    def _load_pwc_net(self, model_path: str):
        """Load PWC-Net model"""
        try:
            model = load_pwc_net(model_path)
            model.to(self.device)
            model.eval()
            print("PWC-Net loaded successfully")
            return model
        except Exception as e:
            print(f"Warning: Could not load PWC-Net from {model_path}: {e}")
            print("PWC-Net disabled - using OpenCV optical flow only")
            return None
    
    def _load_yolo_model(self, model_path: str):
        """Load YOLO model"""
        try:
            model = YOLO(model_path)
            print(f"YOLO model loaded: {model_path}")
            return model
        except Exception as e:
            print(f"Warning: Could not load YOLO from {model_path}: {e}")
            print("Using default YOLOv8n")
            return YOLO("yolov8n.pt")
    
    def _load_speed_model(self, model_type: str):
        """Load speed estimation model"""
        model = create_speed_model(model_type, input_channels=2, dropout_rate=0.3)
        model.to(self.device)
        model.eval()
        print(f"Speed model ({model_type}) initialized")
        return model
    
    def detect_vehicles(self, frame: np.ndarray) -> List[Dict]:
        """
        Detect vehicles in frame using YOLO
        
        Args:
            frame: Input frame
            
        Returns:
            List of vehicle detections
        """
        results = self.yolo_model(frame, conf=self.confidence_threshold)
        
        vehicles = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # Filter for vehicle classes (car, truck, bus, motorcycle)
                    class_id = int(box.cls[0])
                    if class_id in [2, 3, 5, 7]:  # COCO classes for vehicles
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = float(box.conf[0])
                        
                        vehicles.append({
                            'bbox': [int(x1), int(y1), int(x2), int(y2)],
                            'confidence': confidence,
                            'class_id': class_id,
                            'center': [(x1 + x2) / 2, (y1 + y2) / 2]
                        })
        
        return vehicles
    
    def calculate_optical_flow(self, frame1: np.ndarray, frame2: np.ndarray) -> torch.Tensor:
        """
        Calculate optical flow using PWC-Net with fallback to OpenCV

        Args:
            frame1: Previous frame
            frame2: Current frame

        Returns:
            Optical flow tensor
        """
        # If PWC-Net is not available, use OpenCV directly
        if self.pwc_net is None:
            return self._calculate_opencv_flow(frame1, frame2)

        try:
            # Preprocess frames to ensure consistent size
            target_size = (220, 66)
            frame1_resized = cv2.resize(frame1, target_size)
            frame2_resized = cv2.resize(frame2, target_size)

            # Convert frames to tensors
            tensor1, tensor2 = frames_to_tensor(frame1_resized, frame2_resized)

            # Ensure tensors have correct dimensions
            if tensor1.dim() == 3:
                tensor1 = tensor1.unsqueeze(0)  # Add batch dimension
            if tensor2.dim() == 3:
                tensor2 = tensor2.unsqueeze(0)  # Add batch dimension

            # Move to device
            tensor1 = tensor1.to(self.device)
            tensor2 = tensor2.to(self.device)

            # Calculate optical flow using PWC-Net
            with torch.no_grad():
                flow = self.pwc_net(tensor1, tensor2)

            # Ensure flow has correct dimensions (2, H, W)
            if flow.dim() == 4:
                flow = flow.squeeze(0)  # Remove batch dimension

            return flow

        except Exception as e:
            print(f"PWC-Net failed, using OpenCV fallback: {e}")
            return self._calculate_opencv_flow(frame1, frame2)

    def _calculate_opencv_flow(self, frame1: np.ndarray, frame2: np.ndarray) -> torch.Tensor:
        """
        Fallback optical flow calculation using OpenCV Farneback method

        Args:
            frame1: Previous frame
            frame2: Current frame

        Returns:
            Optical flow tensor
        """
        try:
            # Validate input frames
            if frame1 is None or frame2 is None or frame1.size == 0 or frame2.size == 0:
                raise ValueError("Invalid input frames")

            # Preprocess frames
            if len(frame1.shape) == 3:
                gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
            else:
                gray1 = frame1.copy()

            if len(frame2.shape) == 3:
                gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
            else:
                gray2 = frame2.copy()

            # Validate grayscale frames
            if gray1.size == 0 or gray2.size == 0:
                raise ValueError("Empty grayscale frames")

            # Resize to target size for consistency
            target_size = (220, 66)
            gray1 = cv2.resize(gray1, target_size, interpolation=cv2.INTER_AREA)
            gray2 = cv2.resize(gray2, target_size, interpolation=cv2.INTER_AREA)

            # Calculate optical flow using Farneback method
            flow = cv2.calcOpticalFlowFarneback(
                gray1, gray2, None,
                pyr_scale=0.5, levels=3, winsize=15,
                iterations=3, poly_n=5, poly_sigma=1.2, flags=0
            )

            # Convert to tensor format (2, H, W)
            if flow is not None and len(flow.shape) == 3 and flow.shape[-1] == 2:
                flow_u = flow[:, :, 0]
                flow_v = flow[:, :, 1]
            else:
                # Create zero flow as fallback
                flow_u = np.zeros((target_size[1], target_size[0]))
                flow_v = np.zeros((target_size[1], target_size[0]))

            # Convert to torch tensor
            flow_tensor = torch.stack([
                torch.from_numpy(flow_u.astype(np.float32)),
                torch.from_numpy(flow_v.astype(np.float32))
            ]).to(self.device)

            return flow_tensor

        except Exception as e:
            print(f"OpenCV flow calculation failed: {e}")
            # Return zero flow as final fallback
            target_size = (220, 66)
            flow_u = np.zeros((target_size[1], target_size[0]))
            flow_v = np.zeros((target_size[1], target_size[0]))

            flow_tensor = torch.stack([
                torch.from_numpy(flow_u.astype(np.float32)),
                torch.from_numpy(flow_v.astype(np.float32))
            ]).to(self.device)

            return flow_tensor
    
    def estimate_ego_speed(self, flow: torch.Tensor) -> float:
        """
        Estimate ego vehicle speed from optical flow

        Args:
            flow: Optical flow tensor

        Returns:
            Estimated speed in km/h
        """
        try:
            # Simple flow-based speed estimation (more reliable)
            # Calculate flow magnitude
            if flow.dim() == 3 and flow.shape[0] == 2:
                flow_u = flow[0]
                flow_v = flow[1]
            elif flow.dim() == 4 and flow.shape[1] == 2:
                flow_u = flow[0, 0]
                flow_v = flow[0, 1]
            else:
                # Fallback for unexpected dimensions
                flow_flat = flow.view(-1)
                if len(flow_flat) >= 2:
                    flow_u = flow_flat[0]
                    flow_v = flow_flat[1]
                else:
                    return 0.0

            # Calculate magnitude of optical flow
            flow_magnitude = torch.sqrt(flow_u**2 + flow_v**2)

            # Use median to reduce noise
            if flow_magnitude.numel() > 1:
                median_magnitude = torch.median(flow_magnitude)
            else:
                median_magnitude = flow_magnitude

            # Convert to speed with calibration
            # This is a simplified model - in practice you'd need proper calibration
            speed_kmh = float(median_magnitude.cpu().item()) * self.speed_scale * 1000

            # Clamp to reasonable range
            speed_kmh = max(0, min(speed_kmh, 200))

            return speed_kmh

        except Exception as e:
            print(f"Error in speed estimation: {e}")
            # Final fallback
            return 0.0
    
    def track_vehicles(self, vehicles: List[Dict]) -> Dict:
        """
        Simple vehicle tracking based on position
        
        Args:
            vehicles: List of detected vehicles
            
        Returns:
            Dictionary of tracked vehicles
        """
        current_tracks = {}
        
        for vehicle in vehicles:
            center = vehicle['center']
            
            # Find closest existing track
            best_match = None
            min_distance = float('inf')
            
            for track_id, track_data in self.vehicle_tracks.items():
                if len(track_data['positions']) > 0:
                    last_pos = track_data['positions'][-1]
                    distance = np.sqrt((center[0] - last_pos[0])**2 + (center[1] - last_pos[1])**2)
                    
                    if distance < min_distance and distance < 50:  # 50 pixel threshold
                        min_distance = distance
                        best_match = track_id
            
            if best_match is not None:
                # Update existing track
                track_id = best_match
                self.vehicle_tracks[track_id]['positions'].append(center)
                self.vehicle_tracks[track_id]['last_seen'] = self.frame_count
            else:
                # Create new track
                track_id = self.track_id_counter
                self.track_id_counter += 1
                self.vehicle_tracks[track_id] = {
                    'speeds': [],
                    'positions': [center],
                    'last_seen': self.frame_count,
                    'overspeeding': False
                }
            
            # Add vehicle info to current tracks
            current_tracks[track_id] = {
                **vehicle,
                'track_id': track_id
            }
        
        # Clean up old tracks
        self._cleanup_tracks()
        
        return current_tracks
    
    def _cleanup_tracks(self, max_age: int = 30):
        """Remove old tracks"""
        to_remove = []
        for track_id, track_data in self.vehicle_tracks.items():
            if self.frame_count - track_data['last_seen'] > max_age:
                to_remove.append(track_id)
        
        for track_id in to_remove:
            del self.vehicle_tracks[track_id]

    def process_frame(self, frame: np.ndarray) -> Tuple[np.ndarray, float, List[Dict]]:
        """
        Process a single frame for speed detection

        Args:
            frame: Input frame

        Returns:
            Tuple of (processed_frame, ego_speed, vehicle_detections)
        """
        self.frame_count += 1
        ego_speed = 0.0

        # Detect vehicles
        vehicles = self.detect_vehicles(frame)
        tracked_vehicles = self.track_vehicles(vehicles)

        # Calculate optical flow if we have a previous frame
        if self.prev_frame is not None:
            try:
                flow = self.calculate_optical_flow(self.prev_frame, frame)
                ego_speed = self.estimate_ego_speed(flow)

                # Update vehicle speeds based on ego motion
                self._update_vehicle_speeds(tracked_vehicles, ego_speed)

            except Exception as e:
                print(f"Error calculating optical flow: {e}")
                ego_speed = 0.0

        # Draw annotations on frame
        annotated_frame = self._annotate_frame(frame.copy(), tracked_vehicles, ego_speed)

        # Store current frame for next iteration
        self.prev_frame = frame.copy()

        return annotated_frame, ego_speed, list(tracked_vehicles.values())

    def _update_vehicle_speeds(self, tracked_vehicles: Dict, ego_speed: float):
        """Update vehicle speeds based on ego motion"""
        for track_id in tracked_vehicles.keys():
            if track_id in self.vehicle_tracks:
                # Simple relative speed calculation
                # In practice, this would be more sophisticated
                relative_speed = ego_speed * 0.8  # Simplified assumption
                self.vehicle_tracks[track_id]['speeds'].append(relative_speed)

                # Check for overspeeding
                if len(self.vehicle_tracks[track_id]['speeds']) > 5:
                    avg_speed = np.mean(self.vehicle_tracks[track_id]['speeds'][-5:])
                    self.vehicle_tracks[track_id]['overspeeding'] = avg_speed > self.speed_limit

    def _annotate_frame(self, frame: np.ndarray, tracked_vehicles: Dict, ego_speed: float) -> np.ndarray:
        """
        Annotate frame with speed information and vehicle detections

        Args:
            frame: Input frame
            tracked_vehicles: Dictionary of tracked vehicles
            ego_speed: Ego vehicle speed

        Returns:
            Annotated frame
        """
        # Draw ego speed
        speed_text = f"Speed: {ego_speed:.1f} km/h"
        cv2.putText(frame, speed_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX,
                   1, (0, 255, 0), 2)

        # Draw frame count
        frame_text = f"Frame: {self.frame_count}"
        cv2.putText(frame, frame_text, (10, 70), cv2.FONT_HERSHEY_SIMPLEX,
                   0.7, (255, 255, 255), 2)

        # Draw vehicle detections
        for track_id, vehicle in tracked_vehicles.items():
            bbox = vehicle['bbox']
            x1, y1, x2, y2 = bbox

            # Get vehicle info
            track_data = self.vehicle_tracks.get(track_id, {})
            is_overspeeding = track_data.get('overspeeding', False)

            # Choose color based on overspeeding status
            color = (0, 0, 255) if is_overspeeding else (0, 255, 0)

            # Draw bounding box
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)

            # Draw track ID and speed info
            if track_data.get('speeds'):
                avg_speed = np.mean(track_data['speeds'][-5:]) if len(track_data['speeds']) >= 5 else 0
                speed_text = f"ID:{track_id} {avg_speed:.1f}km/h"
            else:
                speed_text = f"ID:{track_id}"

            cv2.putText(frame, speed_text, (x1, y1 - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            # Mark overspeeding vehicles
            if is_overspeeding:
                cv2.putText(frame, "OVERSPEEDING!", (x1, y2 + 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)

        return frame

    def process_video(self, video_path: str, output_path: str = None,
                     max_frames: int = None) -> Dict:
        """
        Process entire video for speed detection

        Args:
            video_path: Path to input video
            output_path: Path to save output video (optional)
            max_frames: Maximum frames to process (optional)

        Returns:
            Processing results dictionary
        """
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Could not open video: {video_path}")

        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        print(f"Processing video: {video_path}")
        print(f"Resolution: {width}x{height}, FPS: {fps}, Total frames: {total_frames}")

        # Setup video writer if output path is provided
        out = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        # Processing statistics
        ego_speeds = []
        violation_frames = []
        processing_times = []

        frame_idx = 0
        start_time = time.time()

        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                if max_frames and frame_idx >= max_frames:
                    break

                frame_start = time.time()

                # Process frame
                processed_frame, ego_speed, vehicles = self.process_frame(frame)

                frame_time = time.time() - frame_start
                processing_times.append(frame_time)

                # Store results
                ego_speeds.append(ego_speed)

                # Check for violations
                for vehicle in vehicles:
                    track_id = vehicle['track_id']
                    if self.vehicle_tracks[track_id].get('overspeeding', False):
                        violation_frames.append({
                            'frame': frame_idx,
                            'track_id': track_id,
                            'ego_speed': ego_speed,
                            'timestamp': frame_idx / fps
                        })

                # Write frame to output video
                if out:
                    out.write(processed_frame)

                frame_idx += 1

                # Progress update
                if frame_idx % 100 == 0:
                    elapsed = time.time() - start_time
                    fps_current = frame_idx / elapsed
                    print(f"Processed {frame_idx}/{total_frames} frames "
                          f"({fps_current:.1f} FPS)")

        finally:
            cap.release()
            if out:
                out.release()

        total_time = time.time() - start_time
        avg_processing_time = np.mean(processing_times) if processing_times else 0

        results = {
            'total_frames': frame_idx,
            'total_time': total_time,
            'avg_processing_time': avg_processing_time,
            'avg_ego_speed': np.mean(ego_speeds) if ego_speeds else 0,
            'max_ego_speed': np.max(ego_speeds) if ego_speeds else 0,
            'violation_frames': violation_frames,
            'output_path': output_path
        }

        print(f"\nProcessing completed:")
        print(f"Total frames: {results['total_frames']}")
        print(f"Total time: {results['total_time']:.2f}s")
        print(f"Average speed: {results['avg_ego_speed']:.1f} km/h")
        print(f"Max speed: {results['max_ego_speed']:.1f} km/h")
        print(f"Violations detected: {len(violation_frames)}")

        return results
